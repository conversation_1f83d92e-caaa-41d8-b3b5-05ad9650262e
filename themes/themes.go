package themes

import (
	"encoding/json"
	"os"
	"path/filepath"

	"github.com/charmbracelet/lipgloss"
)

// ThemeType represents different available themes
type ThemeType string

const (
	DefaultTheme ThemeType = "default"
	SciFiTheme   ThemeType = "scifi"
	MatrixTheme  ThemeType = "matrix"
	CyberTheme   ThemeType = "cyber"
)

// Theme contains all styling information for the CLI
type Theme struct {
	Name        string    `json:"name"`
	Type        ThemeType `json:"type"`
	Colors      Colors    `json:"colors"`
	Borders     Borders   `json:"borders"`
	Typography  Typography `json:"typography"`
	Icons       Icons     `json:"icons"`
}

// Colors defines the color palette for a theme
type Colors struct {
	Primary     string `json:"primary"`
	Secondary   string `json:"secondary"`
	Accent      string `json:"accent"`
	Background  string `json:"background"`
	Foreground  string `json:"foreground"`
	Success     string `json:"success"`
	Warning     string `json:"warning"`
	Error       string `json:"error"`
	Muted       string `json:"muted"`
	Highlight   string `json:"highlight"`
}

// Borders defines border styles
type Borders struct {
	Style     lipgloss.Border `json:"-"`
	StyleName string          `json:"style"`
	Color     string          `json:"color"`
}

// Typography defines text styling
type Typography struct {
	TitleSize   int  `json:"title_size"`
	BodySize    int  `json:"body_size"`
	BoldTitles  bool `json:"bold_titles"`
	ItalicMuted bool `json:"italic_muted"`
}

// Icons defines the icon set for the theme
type Icons struct {
	Arrow       string `json:"arrow"`
	Bullet      string `json:"bullet"`
	Success     string `json:"success"`
	Error       string `json:"error"`
	Warning     string `json:"warning"`
	Info        string `json:"info"`
	Network     string `json:"network"`
	Utility     string `json:"utility"`
	Settings    string `json:"settings"`
	Back        string `json:"back"`
	Generate    string `json:"generate"`
}

// ThemeManager handles theme operations
type ThemeManager struct {
	currentTheme Theme
	configPath   string
}

// NewThemeManager creates a new theme manager
func NewThemeManager() *ThemeManager {
	homeDir, _ := os.UserHomeDir()
	configPath := filepath.Join(homeDir, ".cli-tool-config.json")
	
	tm := &ThemeManager{
		configPath: configPath,
	}
	
	// Load saved theme or use default
	if err := tm.LoadTheme(); err != nil {
		tm.currentTheme = GetDefaultTheme()
	}
	
	return tm
}

// GetCurrentTheme returns the currently active theme
func (tm *ThemeManager) GetCurrentTheme() Theme {
	return tm.currentTheme
}

// SetTheme changes the current theme and saves it
func (tm *ThemeManager) SetTheme(themeType ThemeType) error {
	switch themeType {
	case SciFiTheme:
		tm.currentTheme = GetSciFiTheme()
	case MatrixTheme:
		tm.currentTheme = GetMatrixTheme()
	case CyberTheme:
		tm.currentTheme = GetCyberTheme()
	default:
		tm.currentTheme = GetDefaultTheme()
	}
	
	return tm.SaveTheme()
}

// SaveTheme saves the current theme to disk
func (tm *ThemeManager) SaveTheme() error {
	data, err := json.MarshalIndent(tm.currentTheme, "", "  ")
	if err != nil {
		return err
	}
	
	return os.WriteFile(tm.configPath, data, 0644)
}

// LoadTheme loads the theme from disk
func (tm *ThemeManager) LoadTheme() error {
	data, err := os.ReadFile(tm.configPath)
	if err != nil {
		return err
	}
	
	return json.Unmarshal(data, &tm.currentTheme)
}

// GetAvailableThemes returns all available themes
func GetAvailableThemes() []ThemeType {
	return []ThemeType{DefaultTheme, SciFiTheme, MatrixTheme, CyberTheme}
}

// GetDefaultTheme returns the default theme
func GetDefaultTheme() Theme {
	return Theme{
		Name: "Default",
		Type: DefaultTheme,
		Colors: Colors{
			Primary:    "#6C5B7B",
			Secondary:  "#C0C0C0",
			Accent:     "#F8B195",
			Background: "#000000",
			Foreground: "#FFFFFF",
			Success:    "#4CAF50",
			Warning:    "#FF9800",
			Error:      "#F44336",
			Muted:      "#888888",
			Highlight:  "#FFD700",
		},
		Borders: Borders{
			Style:     lipgloss.DoubleBorder(),
			StyleName: "double",
			Color:     "#6C5B7B",
		},
		Typography: Typography{
			TitleSize:   1,
			BodySize:    1,
			BoldTitles:  true,
			ItalicMuted: false,
		},
		Icons: Icons{
			Arrow:    "▶",
			Bullet:   "•",
			Success:  "✓",
			Error:    "✗",
			Warning:  "⚠",
			Info:     "ℹ",
			Network:  "🌐",
			Utility:  "🔧",
			Settings: "⚙",
			Back:     "←",
			Generate: "⚡",
		},
	}
}

// GetSciFiTheme returns a sci-fi inspired theme
func GetSciFiTheme() Theme {
	return Theme{
		Name: "Sci-Fi",
		Type: SciFiTheme,
		Colors: Colors{
			Primary:    "#00FFFF",
			Secondary:  "#0080FF",
			Accent:     "#FF6B35",
			Background: "#0A0A0A",
			Foreground: "#E0E0E0",
			Success:    "#00FF41",
			Warning:    "#FFD700",
			Error:      "#FF073A",
			Muted:      "#4A90E2",
			Highlight:  "#00FFFF",
		},
		Borders: Borders{
			Style:     lipgloss.RoundedBorder(),
			StyleName: "rounded",
			Color:     "#00FFFF",
		},
		Typography: Typography{
			TitleSize:   1,
			BodySize:    1,
			BoldTitles:  true,
			ItalicMuted: true,
		},
		Icons: Icons{
			Arrow:    "▷",
			Bullet:   "◦",
			Success:  "◉",
			Error:    "◎",
			Warning:  "△",
			Info:     "◈",
			Network:  "⟨⟩",
			Utility:  "⬢",
			Settings: "⚙",
			Back:     "◁",
			Generate: "⟡",
		},
	}
}

// GetMatrixTheme returns a matrix-inspired theme
func GetMatrixTheme() Theme {
	return Theme{
		Name: "Matrix",
		Type: MatrixTheme,
		Colors: Colors{
			Primary:    "#00FF00",
			Secondary:  "#008F00",
			Accent:     "#00FF41",
			Background: "#000000",
			Foreground: "#00FF00",
			Success:    "#00FF41",
			Warning:    "#FFFF00",
			Error:      "#FF0000",
			Muted:      "#004400",
			Highlight:  "#00FF00",
		},
		Borders: Borders{
			Style:     lipgloss.ThickBorder(),
			StyleName: "thick",
			Color:     "#00FF00",
		},
		Typography: Typography{
			TitleSize:   1,
			BodySize:    1,
			BoldTitles:  true,
			ItalicMuted: false,
		},
		Icons: Icons{
			Arrow:    "►",
			Bullet:   "■",
			Success:  "●",
			Error:    "▲",
			Warning:  "▼",
			Info:     "♦",
			Network:  "◊",
			Utility:  "◘",
			Settings: "☰",
			Back:     "◄",
			Generate: "※",
		},
	}
}

// GetCyberTheme returns a cyberpunk-inspired theme
func GetCyberTheme() Theme {
	return Theme{
		Name: "Cyber",
		Type: CyberTheme,
		Colors: Colors{
			Primary:    "#FF00FF",
			Secondary:  "#00FFFF",
			Accent:     "#FFFF00",
			Background: "#1A0033",
			Foreground: "#FF00FF",
			Success:    "#00FF00",
			Warning:    "#FF8000",
			Error:      "#FF0080",
			Muted:      "#800080",
			Highlight:  "#FFFF00",
		},
		Borders: Borders{
			Style:     lipgloss.HeavyBorder(),
			StyleName: "heavy",
			Color:     "#FF00FF",
		},
		Typography: Typography{
			TitleSize:   1,
			BodySize:    1,
			BoldTitles:  true,
			ItalicMuted: true,
		},
		Icons: Icons{
			Arrow:    "⟩",
			Bullet:   "⬢",
			Success:  "⬡",
			Error:    "⬟",
			Warning:  "⬠",
			Info:     "⬣",
			Network:  "⬢",
			Utility:  "⬡",
			Settings: "⚙",
			Back:     "⟨",
			Generate: "⟐",
		},
	}
}
