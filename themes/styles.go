package themes

import (
	"github.com/charmbracelet/lipgloss"
)

// Styles contains all the styled components for the CLI
type Styles struct {
	Title         lipgloss.Style
	Subtitle      lipgloss.Style
	Item          lipgloss.Style
	SelectedItem  lipgloss.Style
	Result        lipgloss.Style
	Success       lipgloss.Style
	Warning       lipgloss.Style
	Error         lipgloss.Style
	Muted         lipgloss.Style
	Highlight     lipgloss.Style
	Input         lipgloss.Style
	Button        lipgloss.Style
	ActiveButton  lipgloss.Style
	Border        lipgloss.Style
	Container     lipgloss.Style
	Header        lipgloss.Style
	Footer        lipgloss.Style
	StatusBar     lipgloss.Style
	HelpText      lipgloss.Style
}

// NewStyles creates a new Styles instance based on the given theme
func NewStyles(theme Theme) Styles {
	// Get border style from theme
	var borderStyle lipgloss.Border
	switch theme.Borders.StyleName {
	case "rounded":
		borderStyle = lipgloss.RoundedBorder()
	case "thick":
		borderStyle = lipgloss.ThickBorder()
	case "heavy":
		borderStyle = lipgloss.HeavyBorder()
	case "normal":
		borderStyle = lipgloss.NormalBorder()
	default:
		borderStyle = lipgloss.DoubleBorder()
	}

	return Styles{
		Title: lipgloss.NewStyle().
			Bold(theme.Typography.BoldTitles).
			Foreground(lipgloss.Color(theme.Colors.Foreground)).
			Background(lipgloss.Color(theme.Colors.Background)).
			BorderStyle(borderStyle).
			BorderForeground(lipgloss.Color(theme.Borders.Color)).
			PaddingLeft(2).
			PaddingRight(2).
			MarginBottom(1),

		Subtitle: lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color(theme.Colors.Primary)).
			MarginBottom(1),

		Item: lipgloss.NewStyle().
			PaddingLeft(4).
			Foreground(lipgloss.Color(theme.Colors.Secondary)),

		SelectedItem: lipgloss.NewStyle().
			PaddingLeft(2).
			Foreground(lipgloss.Color(theme.Colors.Accent)).
			Bold(true).
			Background(lipgloss.Color(theme.Colors.Background)),

		Result: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Accent)).
			Bold(true).
			PaddingTop(1).
			PaddingBottom(1),

		Success: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Success)).
			Bold(true),

		Warning: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Warning)).
			Bold(true),

		Error: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Error)).
			Bold(true),

		Muted: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Muted)).
			Italic(theme.Typography.ItalicMuted),

		Highlight: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Highlight)).
			Bold(true).
			Background(lipgloss.Color(theme.Colors.Background)),

		Input: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Foreground)).
			Background(lipgloss.Color(theme.Colors.Background)).
			BorderStyle(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color(theme.Colors.Primary)).
			PaddingLeft(1).
			PaddingRight(1),

		Button: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Secondary)).
			Background(lipgloss.Color(theme.Colors.Background)).
			BorderStyle(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color(theme.Colors.Secondary)).
			PaddingLeft(2).
			PaddingRight(2),

		ActiveButton: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Background)).
			Background(lipgloss.Color(theme.Colors.Primary)).
			BorderStyle(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color(theme.Colors.Primary)).
			PaddingLeft(2).
			PaddingRight(2).
			Bold(true),

		Border: lipgloss.NewStyle().
			BorderStyle(borderStyle).
			BorderForeground(lipgloss.Color(theme.Borders.Color)),

		Container: lipgloss.NewStyle().
			Padding(1).
			Margin(1),

		Header: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Primary)).
			Background(lipgloss.Color(theme.Colors.Background)).
			Bold(true).
			PaddingLeft(1).
			PaddingRight(1),

		Footer: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Muted)).
			PaddingTop(1),

		StatusBar: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Background)).
			Background(lipgloss.Color(theme.Colors.Primary)).
			PaddingLeft(1).
			PaddingRight(1),

		HelpText: lipgloss.NewStyle().
			Foreground(lipgloss.Color(theme.Colors.Muted)).
			Italic(true).
			PaddingTop(1),
	}
}

// GetIconWithStyle returns a styled icon
func (s Styles) GetIconWithStyle(icon string, style lipgloss.Style) string {
	return style.Render(icon)
}

// FormatWithIcon formats text with an icon prefix
func (s Styles) FormatWithIcon(icon, text string, style lipgloss.Style) string {
	return style.Render(icon + " " + text)
}

// CreateBox creates a bordered box with content
func (s Styles) CreateBox(content string, title string) string {
	boxStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("#6C5B7B")).
		Padding(1).
		Margin(1)
	
	if title != "" {
		titleStyle := lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("#F8B195"))
		content = titleStyle.Render(title) + "\n\n" + content
	}
	
	return boxStyle.Render(content)
}

// CreateProgressBar creates a simple progress bar
func (s Styles) CreateProgressBar(progress float64, width int, theme Theme) string {
	filled := int(progress * float64(width))
	bar := ""
	
	for i := 0; i < width; i++ {
		if i < filled {
			bar += "█"
		} else {
			bar += "░"
		}
	}
	
	progressStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color(theme.Colors.Primary))
	
	return progressStyle.Render(bar)
}

// CreateTable creates a simple table layout
func (s Styles) CreateTable(headers []string, rows [][]string, theme Theme) string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color(theme.Colors.Primary)).
		Background(lipgloss.Color(theme.Colors.Background))
	
	rowStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color(theme.Colors.Secondary))
	
	result := ""
	
	// Headers
	for _, header := range headers {
		result += headerStyle.Render(header) + "\t"
	}
	result += "\n"
	
	// Separator
	for range headers {
		result += "────────\t"
	}
	result += "\n"
	
	// Rows
	for _, row := range rows {
		for _, cell := range row {
			result += rowStyle.Render(cell) + "\t"
		}
		result += "\n"
	}
	
	return result
}
