package main

import (
	"fmt"
	"os"

	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/help"
	"github.com/tirthpatel/cli-tool/themes"
	"github.com/tirthpatel/cli-tool/tools/base64"
	"github.com/tirthpatel/cli-tool/tools/dns"
	"github.com/tirthpatel/cli-tool/tools/hash"
	"github.com/tirthpatel/cli-tool/tools/json"
	"github.com/tirthpatel/cli-tool/tools/portscan"
	"github.com/tirthpatel/cli-tool/tools/sysinfo"
	"github.com/tirthpatel/cli-tool/tools/uuid"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	currentTheme themes.Theme
	styles       themes.Styles
)

type menu int

const (
	mainMenu menu = iota
	toolsMenu
	settingsMenu
)

type category struct {
	name  string
	tools []string
}

type model struct {
	currentMenu      menu
	categories       []category
	cursor           int
	currentTool      tea.Model
	themeOptions     []themes.ThemeType
	settingsItems    []string
	selectedCategory int
	inCategoryView   bool
	inThemeSelection bool
}

func initialModel() model {
	return model{
		currentMenu: mainMenu,
		categories: []category{
			{
				name:  "Network",
				tools: []string{"DNS Checker", "Port Scanner"},
			},
			{
				name:  "Encoding",
				tools: []string{"Base64 Encoder/Decoder", "Hash Generator"},
			},
			{
				name:  "Development",
				tools: []string{"JSON Formatter"},
			},
			{
				name:  "System",
				tools: []string{"UUID Generator", "System Info"},
			},
		},
		themeOptions:  themes.GetAvailableThemes(),
		settingsItems: []string{"Theme Selection", "About"},
	}
}

func (m model) Init() tea.Cmd {
	return nil
}

func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	if m.currentTool != nil {
		newTool, cmd := m.currentTool.Update(msg)

		// Check for back messages from all tools
		if _, ok := newTool.(dns.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}
		if _, ok := newTool.(uuid.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}
		if _, ok := newTool.(base64.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}
		if _, ok := newTool.(hash.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}
		if _, ok := newTool.(json.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}
		if _, ok := newTool.(portscan.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}
		if _, ok := newTool.(sysinfo.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}

		m.currentTool = newTool
		return m, cmd
	}

	switch m.currentMenu {
	case mainMenu:
		return updateMainMenu(msg, m)
	case toolsMenu:
		return updateToolsMenu(msg, m)
	case settingsMenu:
		return updateSettingsMenu(msg, m)
	}

	return m, nil
}

func updateMainMenu(msg tea.Msg, m model) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			if m.cursor < 2 { // 2 items: Tools, Settings
				m.cursor++
			}
		case "enter":
			switch m.cursor {
			case 0:
				m.currentMenu = toolsMenu
				m.cursor = 0
			case 1:
				m.currentMenu = settingsMenu
				m.cursor = 0
			}
		}
	}
	return m, nil
}

func updateToolsMenu(msg tea.Msg, m model) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			if m.inCategoryView {
				m.inCategoryView = false
				m.cursor = m.selectedCategory
			} else {
				m.currentMenu = mainMenu
				m.cursor = 0
			}
			return m, nil
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			maxCursor := len(m.categories) - 1
			if m.inCategoryView {
				maxCursor = len(m.categories[m.selectedCategory].tools) - 1
			}
			if m.cursor < maxCursor {
				m.cursor++
			}
		case "enter":
			if !m.inCategoryView {
				// Entering a category
				m.selectedCategory = m.cursor
				m.inCategoryView = true
				m.cursor = 0
			} else {
				// Selecting a tool
				toolName := m.categories[m.selectedCategory].tools[m.cursor]
				switch toolName {
				case "DNS Checker":
					m.currentTool = dns.New()
				case "Port Scanner":
					m.currentTool = portscan.New()
				case "Base64 Encoder/Decoder":
					m.currentTool = base64.New()
				case "Hash Generator":
					m.currentTool = hash.New()
				case "JSON Formatter":
					m.currentTool = json.New()
				case "UUID Generator":
					m.currentTool = uuid.New()
				case "System Info":
					m.currentTool = sysinfo.New()
				}
				return m, m.currentTool.Init()
			}
		}
	}
	return m, nil
}

func updateSettingsMenu(msg tea.Msg, m model) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			if m.inThemeSelection {
				m.inThemeSelection = false
				m.cursor = 0
			} else {
				m.currentMenu = mainMenu
				m.cursor = 0
			}
			return m, nil
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			maxCursor := len(m.settingsItems) - 1
			if m.inThemeSelection {
				maxCursor = len(m.themeOptions) - 1
			}
			if m.cursor < maxCursor {
				m.cursor++
			}
		case "enter":
			if !m.inThemeSelection {
				switch m.cursor {
				case 0: // Theme Selection
					m.inThemeSelection = true
					m.cursor = 0
				case 1: // About
					return showAbout(m)
				}
			} else {
				// Theme selection
				if m.cursor < len(m.themeOptions) {
					themeManager.SetTheme(m.themeOptions[m.cursor])
					currentTheme = themeManager.GetCurrentTheme()
					styles = themes.NewStyles(currentTheme)
				}
				m.inThemeSelection = false
				m.cursor = 0
			}
		}
	}
	return m, nil
}

func (m model) View() string {
	if m.currentTool != nil {
		return m.currentTool.View()
	}

	switch m.currentMenu {
	case mainMenu:
		return viewMainMenu(m)
	case toolsMenu:
		return viewToolsMenu(m)
	case settingsMenu:
		return viewSettingsMenu(m)
	}

	return ""
}

func viewMainMenu(m model) string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Utility + " Multi-Utility CLI Tool")
	s += "\n\n"

	choices := []string{
		theme.Icons.Utility + " Tools",
		theme.Icons.Settings + " Settings",
	}

	for i, choice := range choices {
		if m.cursor == i {
			s += styles.SelectedItem.Render(theme.Icons.Arrow + " " + choice)
		} else {
			s += styles.Item.Render(choice)
		}
		s += "\n"
	}

	s += "\n" + styles.HelpText.Render("Press q to quit, ↑/↓ to navigate, Enter to select")
	return s
}

func viewToolsMenu(m model) string {
	theme := themeManager.GetCurrentTheme()

	if !m.inCategoryView {
		// Show categories
		s := styles.Title.Render(theme.Icons.Utility + " Tool Categories")
		s += "\n\n"

		for i, category := range m.categories {
			icon := getCategoryIcon(category.name, theme)
			categoryText := icon + " " + category.name

			if m.cursor == i {
				s += styles.SelectedItem.Render(theme.Icons.Arrow + " " + categoryText)
			} else {
				s += styles.Item.Render(categoryText)
			}
			s += "\n"
		}

		s += "\n" + styles.HelpText.Render("Press q to return to main menu, ↑/↓ to navigate, Enter to select category")
		return s
	} else {
		// Show tools in selected category
		category := m.categories[m.selectedCategory]
		s := styles.Title.Render(getCategoryIcon(category.name, theme) + " " + category.name + " Tools")
		s += "\n\n"

		for i, tool := range category.tools {
			icon := getToolIcon(tool, theme)
			toolText := icon + " " + tool

			if m.cursor == i {
				s += styles.SelectedItem.Render(theme.Icons.Arrow + " " + toolText)
			} else {
				s += styles.Item.Render(toolText)
			}
			s += "\n"
		}

		s += "\n" + styles.HelpText.Render("Press q to return to categories, ↑/↓ to navigate, Enter to select tool")
		return s
	}
}

func getCategoryIcon(categoryName string, theme themes.Theme) string {
	switch categoryName {
	case "Network":
		return theme.Icons.Network
	case "Encoding":
		return "🔐"
	case "Development":
		return "💻"
	case "System":
		return theme.Icons.Info
	default:
		return theme.Icons.Utility
	}
}

func getToolIcon(toolName string, theme themes.Theme) string {
	switch toolName {
	case "DNS Checker":
		return theme.Icons.Network
	case "Port Scanner":
		return "🔍"
	case "Base64 Encoder/Decoder":
		return "🔐"
	case "Hash Generator":
		return "🔑"
	case "JSON Formatter":
		return "📄"
	case "UUID Generator":
		return theme.Icons.Generate
	case "System Info":
		return theme.Icons.Info
	default:
		return theme.Icons.Utility
	}
}

func viewSettingsMenu(m model) string {
	theme := themeManager.GetCurrentTheme()

	if !m.inThemeSelection {
		// Show settings menu
		s := styles.Title.Render(theme.Icons.Settings + " Settings")
		s += "\n\n"

		for i, item := range m.settingsItems {
			icon := theme.Icons.Settings
			if item == "About" {
				icon = theme.Icons.Info
			}

			itemText := icon + " " + item
			if m.cursor == i {
				s += styles.SelectedItem.Render(theme.Icons.Arrow + " " + itemText)
			} else {
				s += styles.Item.Render(itemText)
			}
			s += "\n"
		}

		s += "\n" + styles.Muted.Render("Current Theme: " + theme.Name)
		s += "\n" + styles.HelpText.Render("Press q to return to main menu, ↑/↓ to navigate, Enter to select")
		return s
	} else {
		// Show theme selection
		s := styles.Title.Render(theme.Icons.Settings + " Theme Selection")
		s += "\n\n"

		for i, themeType := range m.themeOptions {
			themeName := getThemeName(themeType)

			if themeType == theme.Type {
				s += styles.Success.Render(theme.Icons.Success + " " + themeName + " (Current)")
			} else if i == m.cursor {
				s += styles.SelectedItem.Render(theme.Icons.Arrow + " " + themeName)
			} else {
				s += styles.Item.Render(themeName)
			}
			s += "\n"
		}

		s += "\n" + styles.HelpText.Render("Press Enter to select theme, q to return to settings")
		return s
	}
}

func getThemeName(themeType themes.ThemeType) string {
	switch themeType {
	case themes.DefaultTheme:
		return "Default"
	case themes.SciFiTheme:
		return "Sci-Fi"
	case themes.MatrixTheme:
		return "Matrix"
	case themes.CyberTheme:
		return "Cyber"
	default:
		return string(themeType)
	}
}



func showAbout(m model) (tea.Model, tea.Cmd) {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Info + " About")
	s += "\n\n"
	s += styles.Highlight.Render("Multi-Utility CLI Tool v2.0")
	s += "\n\n"
	s += styles.Item.Render("A modern, themeable CLI utility collection")
	s += "\n"
	s += styles.Item.Render("Built with Bubble Tea and Lip Gloss")
	s += "\n\n"
	s += styles.Muted.Render("Features:")
	s += "\n"
	s += styles.Item.Render("• Multiple visual themes")
	s += "\n"
	s += styles.Item.Render("• Network utilities")
	s += "\n"
	s += styles.Item.Render("• Developer tools")
	s += "\n"
	s += styles.Item.Render("• System utilities")
	s += "\n\n"
	s += styles.HelpText.Render("Press q to return to settings")

	m.currentMenu = settingsMenu
	m.cursor = 1
	return m, nil
}

func main() {
	// Initialize theme system
	themeManager = themes.NewThemeManager()
	currentTheme = themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)

	p := tea.NewProgram(initialModel())
	if err := p.Start(); err != nil {
		fmt.Printf("Alas, there's been an error: %v", err)
		os.Exit(1)
	}
}
