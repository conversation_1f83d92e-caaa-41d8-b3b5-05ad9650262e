package main

import (
	"fmt"
	"os"

	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/tools/dns"
	"github.com/tirthpatel/cli-tool/tools/uuid"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	titleStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("#FFFFFF")).
			Background(lipgloss.Color("#000000")).
			BorderStyle(lipgloss.DoubleBorder()).
			BorderForeground(lipgloss.Color("#6C5B7B")).
			PaddingLeft(2).
			PaddingRight(2)

	itemStyle         = lipgloss.NewStyle().PaddingLeft(4).Foreground(lipgloss.Color("#C0C0C0"))
	selectedItemStyle = lipgloss.NewStyle().PaddingLeft(2).Foreground(lipgloss.Color("#F8B195")).Bold(true)
)

type menu int

const (
	mainMenu menu = iota
	toolsMenu
	settingsMenu
)

type category struct {
	name  string
	tools []string
}

type model struct {
	currentMenu menu
	categories  []category
	cursor      int
	currentTool tea.Model
}

func initialModel() model {
	return model{
		currentMenu: mainMenu,
		categories: []category{
			{
				name:  "Network",
				tools: []string{"DNS Checker"},
			},
			{
				name:  "Utilities",
				tools: []string{"UUID Generator"},
			},
		},
	}
}

func (m model) Init() tea.Cmd {
	return nil
}

func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	if m.currentTool != nil {
		newTool, cmd := m.currentTool.Update(msg)
		if _, ok := newTool.(dns.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}
		if _, ok := newTool.(uuid.BackMsg); ok {
			m.currentTool = nil
			return m, nil
		}
		m.currentTool = newTool
		return m, cmd
	}

	switch m.currentMenu {
	case mainMenu:
		return updateMainMenu(msg, m)
	case toolsMenu:
		return updateToolsMenu(msg, m)
	case settingsMenu:
		return updateSettingsMenu(msg, m)
	}

	return m, nil
}

func updateMainMenu(msg tea.Msg, m model) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			if m.cursor < 2 { // 2 items: Tools, Settings
				m.cursor++
			}
		case "enter":
			switch m.cursor {
			case 0:
				m.currentMenu = toolsMenu
				m.cursor = 0
			case 1:
				m.currentMenu = settingsMenu
				m.cursor = 0
			}
		}
	}
	return m, nil
}

func updateToolsMenu(msg tea.Msg, m model) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			m.currentMenu = mainMenu
			m.cursor = 0
			return m, nil
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			if m.cursor < len(m.categories)-1 {
				m.cursor++
			}
		case "enter":
			// This is a simplified example. A real implementation would
			// have a sub-menu for each category.
			toolName := m.categories[m.cursor].tools[0]
			switch toolName {
			case "DNS Checker":
				m.currentTool = dns.New()
			case "UUID Generator":
				m.currentTool = uuid.New()
			}
			return m, m.currentTool.Init()
		}
	}
	return m, nil
}

func updateSettingsMenu(msg tea.Msg, m model) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			m.currentMenu = mainMenu
			m.cursor = 0
			return m, nil
		}
	}
	return m, nil
}

func (m model) View() string {
	if m.currentTool != nil {
		return m.currentTool.View()
	}

	switch m.currentMenu {
	case mainMenu:
		return viewMainMenu(m)
	case toolsMenu:
		return viewToolsMenu(m)
	case settingsMenu:
		return viewSettingsMenu(m)
	}

	return ""
}

func viewMainMenu(m model) string {
	s := titleStyle.Render("Multi-Utility CLI Tool")
	s += "\n\n"

	choices := []string{"Tools", "Settings"}
	for i, choice := range choices {
		if m.cursor == i {
			s += selectedItemStyle.Render(fmt.Sprintf("> %s", choice))
		} else {
			s += itemStyle.Render(choice)
		}
		s += "\n"
	}
	s += "\nPress q to quit.\n"
	return s
}

func viewToolsMenu(m model) string {
	s := titleStyle.Render("Tools")
	s += "\n\n"

	for i, category := range m.categories {
		if m.cursor == i {
			s += selectedItemStyle.Render(fmt.Sprintf("> %s", category.name))
		} else {
			s += itemStyle.Render(category.name)
		}
		s += "\n"
	}
	s += "\nPress q to return to the main menu.\n"
	return s
}

func viewSettingsMenu(m model) string {
	s := titleStyle.Render("Settings")
	s += "\n\n"
	s += "Settings are not yet implemented.\n"
	s += "\nPress q to return to the main menu.\n"
	return s
}

func main() {
	p := tea.NewProgram(initialModel())
	if err := p.Start(); err != nil {
		fmt.Printf("Alas, there's been an error: %v", err)
		os.Exit(1)
	}
}
