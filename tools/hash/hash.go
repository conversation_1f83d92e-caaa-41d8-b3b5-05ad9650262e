package hash

import (
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/sha512"
	"fmt"
	"hash"

	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/themes"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	styles       themes.Styles
)

func init() {
	themeManager = themes.NewThemeManager()
	currentTheme := themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)
}

type HashType int

const (
	MD5 HashType = iota
	SHA1
	SHA256
	SHA512
)

type Model struct {
	textInput string
	results   map[HashType]string
	cursor    int
	hashTypes []HashType
}

func New() Model {
	return Model{
		results:   make(map[HashType]string),
		hashTypes: []HashType{MD5, SHA1, SHA256, SHA512},
	}
}

func (m Model) Init() tea.Cmd {
	return nil
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd                              { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string                            { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "enter":
			if m.textInput == "" {
				break
			}
			m.generateHashes()
		case "backspace":
			if len(m.textInput) > 0 {
				m.textInput = m.textInput[:len(m.textInput)-1]
				m.results = make(map[HashType]string)
			}
		case "ctrl+u":
			// Clear input
			m.textInput = ""
			m.results = make(map[HashType]string)
		default:
			// Add character to input
			if len(msg.String()) == 1 {
				m.textInput += msg.String()
				m.results = make(map[HashType]string)
			}
		}
	}
	return m, nil
}

func (m *Model) generateHashes() {
	data := []byte(m.textInput)
	
	// MD5
	h := md5.New()
	h.Write(data)
	m.results[MD5] = fmt.Sprintf("%x", h.Sum(nil))
	
	// SHA1
	h = sha1.New()
	h.Write(data)
	m.results[SHA1] = fmt.Sprintf("%x", h.Sum(nil))
	
	// SHA256
	h = sha256.New()
	h.Write(data)
	m.results[SHA256] = fmt.Sprintf("%x", h.Sum(nil))
	
	// SHA512
	h = sha512.New()
	h.Write(data)
	m.results[SHA512] = fmt.Sprintf("%x", h.Sum(nil))
}

func (m Model) getHashName(hashType HashType) string {
	switch hashType {
	case MD5:
		return "MD5"
	case SHA1:
		return "SHA1"
	case SHA256:
		return "SHA256"
	case SHA512:
		return "SHA512"
	default:
		return "Unknown"
	}
}

func (m Model) View() string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Utility + " Hash Generator")
	s += "\n\n"
	
	// Input section
	s += styles.Subtitle.Render("Enter text to hash:")
	s += "\n"
	s += styles.Input.Render(m.textInput + "█")
	s += "\n\n"
	
	// Results section
	if len(m.results) > 0 {
		s += styles.Success.Render(theme.Icons.Success + " Hash Results:")
		s += "\n\n"
		
		for _, hashType := range m.hashTypes {
			if result, exists := m.results[hashType]; exists {
				hashName := m.getHashName(hashType)
				s += styles.Highlight.Render(hashName + ":")
				s += "\n"
				s += styles.Result.Render(result)
				s += "\n\n"
			}
		}
	}
	
	s += styles.HelpText.Render("Enter to generate hashes, Ctrl+U to clear, q to return")
	return s
}
