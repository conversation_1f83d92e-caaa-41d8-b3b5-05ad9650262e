package dns

import (
	"fmt"
	"net"

	"github.com/charmbracelet/lipgloss"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	titleStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("#FFFFFF")).
			Background(lipgloss.Color("#000000")).
			BorderStyle(lipgloss.DoubleBorder()).
			BorderForeground(lipgloss.Color("#6C5B7B")).
			PaddingLeft(2).
			PaddingRight(2)

	resultStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("#F8B195")).Bold(true)
)

type Model struct {
	textInput string
	result    string
}

func New() Model {
	return Model{}
}

func (m Model) Init() tea.Cmd {
	return nil
}

func goBack() tea.Msg {
	return BackMsg{}
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd           { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string          { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "enter":
			ips, err := net.LookupIP(m.textInput)
			if err != nil {
				m.result = fmt.Sprintf("Error: %v", err)
			} else {
				m.result = "IP addresses:\n"
				for _, ip := range ips {
					m.result += ip.String() + "\n"
				}
			}
		case "backspace":
			if len(m.textInput) > 0 {
				m.textInput = m.textInput[:len(m.textInput)-1]
			}
		default:
			m.textInput += msg.String()
		}
	}
	return m, nil
}

func (m Model) View() string {
	s := titleStyle.Render("DNS Checker")
	s += "\n\n"
	s += fmt.Sprintf("Enter a domain name:\n%s\n\n", m.textInput)
	s += resultStyle.Render(m.result)
	s += "\n\nPress 'q' to return to the main menu."
	return s
}
