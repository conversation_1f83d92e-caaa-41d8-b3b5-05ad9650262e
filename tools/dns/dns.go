package dns

import (
	"fmt"
	"net"

	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/themes"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	styles       themes.Styles
)

func init() {
	themeManager = themes.NewThemeManager()
	currentTheme := themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)
}

type Model struct {
	textInput string
	result    string
}

func New() Model {
	return Model{}
}

func (m Model) Init() tea.Cmd {
	return nil
}

func goBack() tea.Msg {
	return BackMsg{}
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd           { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string          { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "enter":
			ips, err := net.LookupIP(m.textInput)
			if err != nil {
				m.result = fmt.Sprintf("Error: %v", err)
			} else {
				m.result = "IP addresses:\n"
				for _, ip := range ips {
					m.result += ip.String() + "\n"
				}
			}
		case "backspace":
			if len(m.textInput) > 0 {
				m.textInput = m.textInput[:len(m.textInput)-1]
			}
		default:
			m.textInput += msg.String()
		}
	}
	return m, nil
}

func (m Model) View() string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Network + " DNS Checker")
	s += "\n\n"

	// Input section
	s += styles.Subtitle.Render("Enter a domain name:")
	s += "\n"
	s += styles.Input.Render(m.textInput + "█") // Add cursor
	s += "\n\n"

	// Results section
	if m.result != "" {
		if len(m.result) > 5 && m.result[:5] == "Error" {
			s += styles.Error.Render(theme.Icons.Error + " " + m.result)
		} else {
			s += styles.Success.Render(theme.Icons.Success + " DNS Resolution Results:")
			s += "\n"
			s += styles.Result.Render(m.result)
		}
	}

	s += "\n\n" + styles.HelpText.Render("Press Enter to resolve, q to return to main menu")
	return s
}
