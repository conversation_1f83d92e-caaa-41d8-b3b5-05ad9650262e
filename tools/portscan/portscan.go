package portscan

import (
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/themes"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	styles       themes.Styles
)

func init() {
	themeManager = themes.NewThemeManager()
	currentTheme := themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)
}

type Model struct {
	hostInput   string
	portInput   string
	results     []ScanResult
	scanning    bool
	cursor      int
	inputMode   int // 0 = host, 1 = port
}

type ScanResult struct {
	Port   int
	Status string
	Service string
}

func New() Model {
	return Model{
		hostInput: "localhost",
		portInput: "80,443,22,21,25,53,110,993,995",
		inputMode: 0,
	}
}

func (m Model) Init() tea.Cmd {
	return nil
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd                              { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string                            { return "" }

type scanCompleteMsg struct {
	results []ScanResult
}

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		if m.scanning {
			// Only allow quit during scanning
			if msg.String() == "ctrl+c" {
				return m, tea.Quit
			}
			return m, nil
		}
		
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "tab":
			// Switch between host and port input
			if m.inputMode == 0 {
				m.inputMode = 1
			} else {
				m.inputMode = 0
			}
		case "enter":
			if m.hostInput == "" || m.portInput == "" {
				break
			}
			m.scanning = true
			m.results = []ScanResult{}
			return m, m.scanPorts()
		case "backspace":
			if m.inputMode == 0 && len(m.hostInput) > 0 {
				m.hostInput = m.hostInput[:len(m.hostInput)-1]
			} else if m.inputMode == 1 && len(m.portInput) > 0 {
				m.portInput = m.portInput[:len(m.portInput)-1]
			}
		case "ctrl+u":
			// Clear current input
			if m.inputMode == 0 {
				m.hostInput = ""
			} else {
				m.portInput = ""
			}
		default:
			// Add character to current input
			if len(msg.String()) == 1 {
				if m.inputMode == 0 {
					m.hostInput += msg.String()
				} else {
					m.portInput += msg.String()
				}
			}
		}
	case scanCompleteMsg:
		m.scanning = false
		m.results = msg.results
	}
	return m, nil
}

func (m Model) scanPorts() tea.Cmd {
	return func() tea.Msg {
		ports := parsePorts(m.portInput)
		results := []ScanResult{}
		
		for _, port := range ports {
			result := ScanResult{
				Port:    port,
				Status:  "Closed",
				Service: getServiceName(port),
			}
			
			address := fmt.Sprintf("%s:%d", m.hostInput, port)
			conn, err := net.DialTimeout("tcp", address, 2*time.Second)
			if err == nil {
				result.Status = "Open"
				conn.Close()
			}
			
			results = append(results, result)
		}
		
		return scanCompleteMsg{results: results}
	}
}

func parsePorts(portStr string) []int {
	var ports []int
	parts := strings.Split(portStr, ",")
	
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.Contains(part, "-") {
			// Range like "80-90"
			rangeParts := strings.Split(part, "-")
			if len(rangeParts) == 2 {
				start, err1 := strconv.Atoi(strings.TrimSpace(rangeParts[0]))
				end, err2 := strconv.Atoi(strings.TrimSpace(rangeParts[1]))
				if err1 == nil && err2 == nil && start <= end {
					for i := start; i <= end; i++ {
						ports = append(ports, i)
					}
				}
			}
		} else {
			// Single port
			if port, err := strconv.Atoi(part); err == nil {
				ports = append(ports, port)
			}
		}
	}
	
	return ports
}

func getServiceName(port int) string {
	services := map[int]string{
		21:   "FTP",
		22:   "SSH",
		23:   "Telnet",
		25:   "SMTP",
		53:   "DNS",
		80:   "HTTP",
		110:  "POP3",
		143:  "IMAP",
		443:  "HTTPS",
		993:  "IMAPS",
		995:  "POP3S",
		3306: "MySQL",
		5432: "PostgreSQL",
		6379: "Redis",
		8080: "HTTP-Alt",
	}
	
	if service, exists := services[port]; exists {
		return service
	}
	return "Unknown"
}

func (m Model) View() string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Network + " Port Scanner")
	s += "\n\n"
	
	// Input section
	hostStyle := styles.Input
	portStyle := styles.Input
	if m.inputMode == 0 {
		hostStyle = styles.ActiveButton
	} else {
		portStyle = styles.ActiveButton
	}
	
	s += styles.Subtitle.Render("Target Host:")
	s += "\n"
	s += hostStyle.Render(m.hostInput + "█")
	s += "\n\n"
	
	s += styles.Subtitle.Render("Ports (comma-separated or ranges like 80-90):")
	s += "\n"
	s += portStyle.Render(m.portInput + "█")
	s += "\n\n"
	
	if m.scanning {
		s += styles.Warning.Render(theme.Icons.Warning + " Scanning ports...")
		s += "\n"
		s += styles.CreateProgressBar(0.5, 30, theme)
	} else if len(m.results) > 0 {
		s += styles.Success.Render(theme.Icons.Success + " Scan Results:")
		s += "\n\n"
		
		// Create table headers
		headers := []string{"Port", "Status", "Service"}
		rows := [][]string{}
		
		for _, result := range m.results {
			status := result.Status
			if result.Status == "Open" {
				status = styles.Success.Render("Open")
			} else {
				status = styles.Muted.Render("Closed")
			}
			
			rows = append(rows, []string{
				fmt.Sprintf("%d", result.Port),
				status,
				result.Service,
			})
		}
		
		s += styles.CreateTable(headers, rows, theme)
	}
	
	s += "\n\n" + styles.HelpText.Render("Tab to switch input, Enter to scan, Ctrl+U to clear, q to return")
	return s
}
