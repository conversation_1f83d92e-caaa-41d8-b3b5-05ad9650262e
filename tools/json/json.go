package json

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/themes"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	styles       themes.Styles
)

func init() {
	themeManager = themes.NewThemeManager()
	currentTheme := themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)
}

type Mode int

const (
	Format Mode = iota
	Minify
	Validate
)

type Model struct {
	textInput string
	result    string
	mode      Mode
	cursor    int
}

func New() Model {
	return Model{
		mode: Format,
	}
}

func (m Model) Init() tea.Cmd {
	return nil
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd                              { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string                            { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "tab":
			// Cycle through modes
			switch m.mode {
			case Format:
				m.mode = Minify
			case Minify:
				m.mode = Validate
			case Validate:
				m.mode = Format
			}
			m.result = ""
		case "enter":
			if m.textInput == "" {
				m.result = "Please enter some JSON"
				break
			}
			m.processJSON()
		case "backspace":
			if len(m.textInput) > 0 {
				m.textInput = m.textInput[:len(m.textInput)-1]
				m.result = ""
			}
		case "ctrl+u":
			// Clear input
			m.textInput = ""
			m.result = ""
		default:
			// Add character to input
			if len(msg.String()) == 1 {
				m.textInput += msg.String()
				m.result = ""
			}
		}
	}
	return m, nil
}

func (m *Model) processJSON() {
	var jsonData interface{}
	
	// First, try to parse the JSON
	if err := json.Unmarshal([]byte(m.textInput), &jsonData); err != nil {
		m.result = fmt.Sprintf("Invalid JSON: %v", err)
		return
	}
	
	switch m.mode {
	case Format:
		formatted, err := json.MarshalIndent(jsonData, "", "  ")
		if err != nil {
			m.result = fmt.Sprintf("Error formatting JSON: %v", err)
		} else {
			m.result = string(formatted)
		}
	case Minify:
		minified, err := json.Marshal(jsonData)
		if err != nil {
			m.result = fmt.Sprintf("Error minifying JSON: %v", err)
		} else {
			m.result = string(minified)
		}
	case Validate:
		m.result = "✓ Valid JSON"
		
		// Add some stats
		jsonStr := m.textInput
		lines := strings.Count(jsonStr, "\n") + 1
		chars := len(jsonStr)
		
		m.result += fmt.Sprintf("\n\nStatistics:\n• Lines: %d\n• Characters: %d\n• Size: %d bytes", 
			lines, chars, len([]byte(jsonStr)))
	}
}

func (m Model) getModeText() string {
	switch m.mode {
	case Format:
		return "Format"
	case Minify:
		return "Minify"
	case Validate:
		return "Validate"
	default:
		return "Unknown"
	}
}

func (m Model) View() string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Utility + " JSON Formatter")
	s += "\n\n"
	
	// Mode selection
	formatStyle := styles.Button
	minifyStyle := styles.Button
	validateStyle := styles.Button
	
	switch m.mode {
	case Format:
		formatStyle = styles.ActiveButton
	case Minify:
		minifyStyle = styles.ActiveButton
	case Validate:
		validateStyle = styles.ActiveButton
	}
	
	s += formatStyle.Render("Format") + "  " + 
		minifyStyle.Render("Minify") + "  " + 
		validateStyle.Render("Validate")
	s += "\n\n"
	
	// Input section
	s += styles.Subtitle.Render("Enter JSON:")
	s += "\n"
	s += styles.Input.Render(m.textInput + "█")
	s += "\n\n"
	
	// Results section
	if m.result != "" {
		if strings.HasPrefix(m.result, "Invalid JSON") || strings.HasPrefix(m.result, "Error") {
			s += styles.Error.Render(theme.Icons.Error + " " + m.result)
		} else {
			resultTitle := m.getModeText() + " Result:"
			if m.mode == Validate {
				s += styles.Success.Render(theme.Icons.Success + " " + m.result)
			} else {
				s += styles.Success.Render(theme.Icons.Success + " " + resultTitle)
				s += "\n"
				s += styles.CreateBox(styles.Result.Render(m.result), "")
			}
		}
	}
	
	s += "\n\n" + styles.HelpText.Render("Tab to switch mode, Enter to process, Ctrl+U to clear, q to return")
	return s
}
