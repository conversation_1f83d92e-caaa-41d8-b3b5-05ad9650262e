package base64

import (
	"encoding/base64"
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/themes"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	styles       themes.Styles
)

func init() {
	themeManager = themes.NewThemeManager()
	currentTheme := themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)
}

type Mode int

const (
	Encode Mode = iota
	Decode
)

type Model struct {
	textInput string
	result    string
	mode      Mode
	cursor    int
}

func New() Model {
	return Model{
		mode: Encode,
	}
}

func (m Model) Init() tea.Cmd {
	return nil
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd                              { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string                            { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "tab":
			// Switch between encode and decode modes
			if m.mode == Encode {
				m.mode = Decode
			} else {
				m.mode = Encode
			}
			m.result = ""
		case "enter":
			if m.textInput == "" {
				m.result = "Please enter some text"
				break
			}
			
			if m.mode == Encode {
				m.result = base64.StdEncoding.EncodeToString([]byte(m.textInput))
			} else {
				decoded, err := base64.StdEncoding.DecodeString(m.textInput)
				if err != nil {
					m.result = fmt.Sprintf("Error: %v", err)
				} else {
					m.result = string(decoded)
				}
			}
		case "backspace":
			if len(m.textInput) > 0 {
				m.textInput = m.textInput[:len(m.textInput)-1]
				m.result = ""
			}
		case "ctrl+u":
			// Clear input
			m.textInput = ""
			m.result = ""
		default:
			// Add character to input
			if len(msg.String()) == 1 {
				m.textInput += msg.String()
				m.result = ""
			}
		}
	}
	return m, nil
}

func (m Model) View() string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Utility + " Base64 Encoder/Decoder")
	s += "\n\n"
	
	// Mode selection
	encodeStyle := styles.Button
	decodeStyle := styles.Button
	if m.mode == Encode {
		encodeStyle = styles.ActiveButton
	} else {
		decodeStyle = styles.ActiveButton
	}
	
	s += encodeStyle.Render("Encode") + "  " + decodeStyle.Render("Decode")
	s += "\n\n"
	
	// Input section
	modeText := "text to encode"
	if m.mode == Decode {
		modeText = "base64 to decode"
	}
	
	s += styles.Subtitle.Render("Enter " + modeText + ":")
	s += "\n"
	s += styles.Input.Render(m.textInput + "█")
	s += "\n\n"
	
	// Results section
	if m.result != "" {
		if strings.HasPrefix(m.result, "Error") {
			s += styles.Error.Render(theme.Icons.Error + " " + m.result)
		} else {
			resultTitle := "Encoded Result:"
			if m.mode == Decode {
				resultTitle = "Decoded Result:"
			}
			s += styles.Success.Render(theme.Icons.Success + " " + resultTitle)
			s += "\n"
			s += styles.CreateBox(styles.Result.Render(m.result), "")
		}
	}
	
	s += "\n\n" + styles.HelpText.Render("Tab to switch mode, Enter to process, Ctrl+U to clear, q to return")
	return s
}
