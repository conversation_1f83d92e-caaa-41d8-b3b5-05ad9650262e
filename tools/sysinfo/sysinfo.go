package sysinfo

import (
	"fmt"
	"os"
	"runtime"
	"time"

	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/themes"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	styles       themes.Styles
)

func init() {
	themeManager = themes.NewThemeManager()
	currentTheme := themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)
}

type Model struct {
	info SystemInfo
}

type SystemInfo struct {
	OS           string
	Architecture string
	CPUs         int
	GoVersion    string
	Hostname     string
	Username     string
	WorkingDir   string
	HomeDir      string
	TempDir      string
	Uptime       time.Duration
}

func New() Model {
	return Model{
		info: gatherSystemInfo(),
	}
}

func (m Model) Init() tea.Cmd {
	return nil
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd                              { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string                            { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "r":
			// Refresh system info
			m.info = gatherSystemInfo()
		}
	}
	return m, nil
}

func gatherSystemInfo() SystemInfo {
	hostname, _ := os.Hostname()
	username := os.Getenv("USER")
	if username == "" {
		username = os.Getenv("USERNAME") // Windows
	}
	workingDir, _ := os.Getwd()
	homeDir, _ := os.UserHomeDir()
	tempDir := os.TempDir()
	
	// Calculate uptime (approximate)
	startTime := time.Now().Add(-time.Duration(runtime.NumGoroutine()) * time.Millisecond)
	uptime := time.Since(startTime)
	
	return SystemInfo{
		OS:           runtime.GOOS,
		Architecture: runtime.GOARCH,
		CPUs:         runtime.NumCPU(),
		GoVersion:    runtime.Version(),
		Hostname:     hostname,
		Username:     username,
		WorkingDir:   workingDir,
		HomeDir:      homeDir,
		TempDir:      tempDir,
		Uptime:       uptime,
	}
}

func (m Model) View() string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Info + " System Information")
	s += "\n\n"
	
	// System section
	s += styles.Subtitle.Render("System:")
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Operating System: %s", m.info.OS))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Architecture: %s", m.info.Architecture))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("CPU Cores: %d", m.info.CPUs))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Go Version: %s", m.info.GoVersion))
	s += "\n\n"
	
	// User section
	s += styles.Subtitle.Render("User Environment:")
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Hostname: %s", m.info.Hostname))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Username: %s", m.info.Username))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Home Directory: %s", m.info.HomeDir))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Working Directory: %s", m.info.WorkingDir))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Temp Directory: %s", m.info.TempDir))
	s += "\n\n"
	
	// Runtime section
	s += styles.Subtitle.Render("Runtime:")
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Goroutines: %d", runtime.NumGoroutine()))
	s += "\n"
	
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	s += styles.Item.Render(fmt.Sprintf("Memory Allocated: %.2f MB", float64(memStats.Alloc)/1024/1024))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("Total Allocations: %.2f MB", float64(memStats.TotalAlloc)/1024/1024))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("System Memory: %.2f MB", float64(memStats.Sys)/1024/1024))
	s += "\n"
	s += styles.Item.Render(fmt.Sprintf("GC Cycles: %d", memStats.NumGC))
	s += "\n\n"
	
	s += styles.HelpText.Render("Press 'r' to refresh, 'q' to return to main menu")
	return s
}
