package uuid

import (
	"fmt"

	"github.com/charmbracelet/lipgloss"
	"github.com/google/uuid"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	titleStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("#FFFFFF")).
			Background(lipgloss.Color("#000000")).
			BorderStyle(lipgloss.DoubleBorder()).
			BorderForeground(lipgloss.Color("#6C5B7B")).
			PaddingLeft(2).
			PaddingRight(2)

	uuidStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("#F8B195")).Bold(true)
)

type Model struct {
	uuid string
}

func New() Model {
	return Model{
		uuid: uuid.New().String(),
	}
}

func (m Model) Init() tea.Cmd {
	return nil
}

func goBack() tea.Msg {
	return BackMsg{}
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd           { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string          { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "r":
			m.uuid = uuid.New().String()
		}
	}
	return m, nil
}

func (m Model) View() string {
	s := titleStyle.Render("UUID Generator")
	s += "\n\n"
	s += fmt.Sprintf("Generated UUID:\n%s\n\n", uuidStyle.Render(m.uuid))
	s += "Press 'r' to generate a new UUID, 'q' to return to the main menu."
	return s
}
