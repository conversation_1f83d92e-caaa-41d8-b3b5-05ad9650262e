package uuid

import (
	"fmt"

	"github.com/charmbracelet/lipgloss"
	"github.com/google/uuid"
	"github.com/tirthpatel/cli-tool/themes"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	styles       themes.Styles
)

func init() {
	themeManager = themes.NewThemeManager()
	currentTheme := themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)
}

type Model struct {
	uuid string
}

func New() Model {
	return Model{
		uuid: uuid.New().String(),
	}
}

func (m Model) Init() tea.Cmd {
	return nil
}

func goBack() tea.Msg {
	return BackMsg{}
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd           { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string          { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "r":
			m.uuid = uuid.New().String()
		}
	}
	return m, nil
}

func (m Model) View() string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Generate + " UUID Generator")
	s += "\n\n"

	s += styles.Subtitle.Render("Generated UUID:")
	s += "\n"
	s += styles.CreateBox(styles.Result.Render(m.uuid), "")
	s += "\n"

	s += styles.HelpText.Render("Press 'r' to generate new UUID, 'q' to return to main menu")
	return s
}
