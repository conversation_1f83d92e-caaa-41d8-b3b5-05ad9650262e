package help

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/tirthpatel/cli-tool/themes"
	tea "github.com/charmbracelet/bubbletea"
)

var (
	themeManager *themes.ThemeManager
	styles       themes.Styles
)

func init() {
	themeManager = themes.NewThemeManager()
	currentTheme := themeManager.GetCurrentTheme()
	styles = themes.NewStyles(currentTheme)
}

type Model struct {
	section int // 0 = general, 1 = navigation, 2 = tools
}

func New() Model {
	return Model{
		section: 0,
	}
}

func (m Model) Init() tea.Cmd {
	return nil
}

type BackMsg struct{}

func (b BackMsg) Init() tea.Cmd                              { return nil }
func (b BackMsg) Update(msg tea.Msg) (tea.Model, tea.Cmd) { return b, nil }
func (b BackMsg) View() string                            { return "" }

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			return BackMsg{}, nil
		case "left", "h":
			if m.section > 0 {
				m.section--
			}
		case "right", "l":
			if m.section < 2 {
				m.section++
			}
		case "tab":
			m.section = (m.section + 1) % 3
		}
	}
	return m, nil
}

func (m Model) View() string {
	theme := themeManager.GetCurrentTheme()
	s := styles.Title.Render(theme.Icons.Info + " Help & Documentation")
	s += "\n\n"
	
	// Section tabs
	generalStyle := styles.Button
	navStyle := styles.Button
	toolsStyle := styles.Button
	
	switch m.section {
	case 0:
		generalStyle = styles.ActiveButton
	case 1:
		navStyle = styles.ActiveButton
	case 2:
		toolsStyle = styles.ActiveButton
	}
	
	s += generalStyle.Render("General") + "  " + 
		navStyle.Render("Navigation") + "  " + 
		toolsStyle.Render("Tools")
	s += "\n\n"
	
	switch m.section {
	case 0:
		s += m.renderGeneralHelp(theme)
	case 1:
		s += m.renderNavigationHelp(theme)
	case 2:
		s += m.renderToolsHelp(theme)
	}
	
	s += "\n\n" + styles.HelpText.Render("Tab/←/→ to switch sections, q to return")
	return s
}

func (m Model) renderGeneralHelp(theme themes.Theme) string {
	s := styles.Subtitle.Render("General Information")
	s += "\n\n"
	
	s += styles.Item.Render("Welcome to the Multi-Utility CLI Tool!")
	s += "\n"
	s += styles.Item.Render("This tool provides various utilities for developers and system administrators.")
	s += "\n\n"
	
	s += styles.Highlight.Render("Features:")
	s += "\n"
	s += styles.Item.Render("• Multiple visual themes (Default, Sci-Fi, Matrix, Cyber)")
	s += "\n"
	s += styles.Item.Render("• Network utilities (DNS checking, port scanning)")
	s += "\n"
	s += styles.Item.Render("• Encoding tools (Base64, hash generation)")
	s += "\n"
	s += styles.Item.Render("• Development tools (JSON formatting)")
	s += "\n"
	s += styles.Item.Render("• System utilities (UUID generation, system info)")
	s += "\n\n"
	
	s += styles.Highlight.Render("Global Shortcuts:")
	s += "\n"
	s += styles.Item.Render("• Ctrl+C: Quit application")
	s += "\n"
	s += styles.Item.Render("• q: Go back/return to previous menu")
	s += "\n"
	s += styles.Item.Render("• ↑/↓ or k/j: Navigate up/down")
	s += "\n"
	s += styles.Item.Render("• Enter: Select/confirm")
	
	return s
}

func (m Model) renderNavigationHelp(theme themes.Theme) string {
	s := styles.Subtitle.Render("Navigation Guide")
	s += "\n\n"
	
	s += styles.Highlight.Render("Main Menu:")
	s += "\n"
	s += styles.Item.Render("• Tools: Access all utility categories")
	s += "\n"
	s += styles.Item.Render("• Settings: Configure themes and view about info")
	s += "\n\n"
	
	s += styles.Highlight.Render("Tools Menu:")
	s += "\n"
	s += styles.Item.Render("• First level: Select tool category")
	s += "\n"
	s += styles.Item.Render("• Second level: Select specific tool")
	s += "\n"
	s += styles.Item.Render("• Press q to go back to previous level")
	s += "\n\n"
	
	s += styles.Highlight.Render("Settings Menu:")
	s += "\n"
	s += styles.Item.Render("• Theme Selection: Choose visual theme")
	s += "\n"
	s += styles.Item.Render("• About: View application information")
	s += "\n\n"
	
	s += styles.Highlight.Render("Tool-Specific Navigation:")
	s += "\n"
	s += styles.Item.Render("• Each tool has its own controls")
	s += "\n"
	s += styles.Item.Render("• Common: Tab to switch modes/inputs")
	s += "\n"
	s += styles.Item.Render("• Common: Enter to execute/process")
	s += "\n"
	s += styles.Item.Render("• Common: Ctrl+U to clear input")
	
	return s
}

func (m Model) renderToolsHelp(theme themes.Theme) string {
	s := styles.Subtitle.Render("Tools Reference")
	s += "\n\n"
	
	s += styles.Highlight.Render("Network Tools:")
	s += "\n"
	s += styles.Item.Render("• DNS Checker: Resolve domain names to IP addresses")
	s += "\n"
	s += styles.Item.Render("• Port Scanner: Check if ports are open on a host")
	s += "\n\n"
	
	s += styles.Highlight.Render("Encoding Tools:")
	s += "\n"
	s += styles.Item.Render("• Base64 Encoder/Decoder: Encode/decode Base64 strings")
	s += "\n"
	s += styles.Item.Render("• Hash Generator: Generate MD5, SHA1, SHA256, SHA512 hashes")
	s += "\n\n"
	
	s += styles.Highlight.Render("Development Tools:")
	s += "\n"
	s += styles.Item.Render("• JSON Formatter: Format, minify, or validate JSON")
	s += "\n\n"
	
	s += styles.Highlight.Render("System Tools:")
	s += "\n"
	s += styles.Item.Render("• UUID Generator: Generate random UUIDs")
	s += "\n"
	s += styles.Item.Render("• System Info: View system and runtime information")
	s += "\n\n"
	
	s += styles.Highlight.Render("Tool Tips:")
	s += "\n"
	s += styles.Item.Render("• Most tools support real-time input")
	s += "\n"
	s += styles.Item.Render("• Use Tab to switch between modes where available")
	s += "\n"
	s += styles.Item.Render("• Results are displayed immediately after processing")
	
	return s
}
